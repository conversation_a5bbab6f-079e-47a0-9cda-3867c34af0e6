#!/usr/bin/env python3
"""
Compare GMM-generated labels with ground truth labels.
Calculates Dice coefficient, IoU, and other metrics for brain segmentation evaluation.

Usage:
    python gmm_loss.py --pred_dir ./synthstrip_data_v1.5_2d_gmm2_exp5 --gt_dir ./synthstrip_data_v1.5_2d --prefix fsm_t1
"""

import argparse
import sys
from pathlib import Path
import numpy as np
import nibabel as nib
import pandas as pd
from typing import Dict, List, Tuple, Optional

def load_nii(path: Path) -> np.ndarray:
    """Load NIfTI file and return data as numpy array."""
    return nib.load(str(path)).get_fdata().astype(np.float32)

def discover_cases(root: Path, prefix: Optional[str] = None) -> List[Path]:
    """Find all case directories that contain labels.nii.gz files."""
    cases = []
    for case_dir in sorted([x for x in root.iterdir() if x.is_dir()]):
        if prefix and prefix not in case_dir.name:
            continue
        labels_path = case_dir / "labels.nii.gz"
        if labels_path.exists():
            cases.append(case_dir)
    return cases

def apply_mapping(labels: np.ndarray, mapping_path: Path) -> np.ndarray:
    """Apply mapping CSV to convert multi-class labels to binary."""
    if not mapping_path.exists():
        print(f"Warning: Mapping file not found at {mapping_path}")
        return (labels > 0).astype(np.uint8)
    
    try:
        mapping_df = pd.read_csv(mapping_path)
        if 'label' not in mapping_df.columns:
            raise ValueError("CSV must have a 'label' column")
        
        # Choose mapping column flexibly
        value_col = 'mapping' if 'mapping' in mapping_df.columns else 'out'
        if value_col not in mapping_df.columns:
            raise ValueError("CSV must have a 'mapping' or 'out' column")
        
        # Create mapping dictionary
        mapping_dict = dict(zip(mapping_df['label'].astype(int), 
                               mapping_df[value_col].astype(int)))
        
        # Apply mapping
        binary_labels = np.zeros_like(labels, dtype=np.uint8)
        for label_id, binary_val in mapping_dict.items():
            binary_labels[labels == label_id] = binary_val
            
        return binary_labels
        
    except Exception as e:
        print(f"Error applying mapping: {e}")
        return (labels > 0).astype(np.uint8)

def calculate_dice(pred: np.ndarray, gt: np.ndarray) -> float:
    """Calculate Dice coefficient between prediction and ground truth."""
    pred_bool = pred > 0
    gt_bool = gt > 0
    
    intersection = np.sum(pred_bool & gt_bool)
    total = np.sum(pred_bool) + np.sum(gt_bool)
    
    if total == 0:
        return 1.0 if intersection == 0 else 0.0
    
    return 2.0 * intersection / total

def calculate_iou(pred: np.ndarray, gt: np.ndarray) -> float:
    """Calculate Intersection over Union (IoU/Jaccard) coefficient."""
    pred_bool = pred > 0
    gt_bool = gt > 0
    
    intersection = np.sum(pred_bool & gt_bool)
    union = np.sum(pred_bool | gt_bool)
    
    if union == 0:
        return 1.0 if intersection == 0 else 0.0
    
    return intersection / union

def calculate_sensitivity(pred: np.ndarray, gt: np.ndarray) -> float:
    """Calculate sensitivity (recall/true positive rate)."""
    pred_bool = pred > 0
    gt_bool = gt > 0
    
    true_positives = np.sum(pred_bool & gt_bool)
    actual_positives = np.sum(gt_bool)
    
    if actual_positives == 0:
        return 1.0 if true_positives == 0 else 0.0
    
    return true_positives / actual_positives

def calculate_specificity(pred: np.ndarray, gt: np.ndarray) -> float:
    """Calculate specificity (true negative rate)."""
    pred_bool = pred > 0
    gt_bool = gt > 0
    
    true_negatives = np.sum(~pred_bool & ~gt_bool)
    actual_negatives = np.sum(~gt_bool)
    
    if actual_negatives == 0:
        return 1.0 if true_negatives == 0 else 0.0
    
    return true_negatives / actual_negatives

def calculate_hausdorff_distance(pred: np.ndarray, gt: np.ndarray) -> float:
    """Calculate Hausdorff distance (simplified version using surface points)."""
    try:
        from scipy.spatial.distance import directed_hausdorff
        from scipy.ndimage import binary_erosion
        
        pred_bool = pred > 0
        gt_bool = gt > 0
        
        # Get surface points by erosion
        pred_surface = pred_bool & ~binary_erosion(pred_bool)
        gt_surface = gt_bool & ~binary_erosion(gt_bool)
        
        if not np.any(pred_surface) or not np.any(gt_surface):
            return float('inf')
        
        pred_coords = np.column_stack(np.where(pred_surface))
        gt_coords = np.column_stack(np.where(gt_surface))
        
        return max(directed_hausdorff(pred_coords, gt_coords)[0],
                  directed_hausdorff(gt_coords, pred_coords)[0])
    except ImportError:
        return float('nan')

def evaluate_case(pred_dir: Path, gt_dir: Path, case_name: str, 
                 pred_mapping_path: Optional[Path] = None,
                 gt_mapping_path: Optional[Path] = None) -> Dict[str, float]:
    """Evaluate a single case and return metrics."""
    
    # Load prediction labels
    pred_labels_path = pred_dir / case_name / "labels.nii.gz"
    if not pred_labels_path.exists():
        raise FileNotFoundError(f"Prediction labels not found: {pred_labels_path}")
    
    # Load ground truth labels
    gt_labels_path = gt_dir / case_name / "labels.nii.gz"
    if not gt_labels_path.exists():
        raise FileNotFoundError(f"Ground truth labels not found: {gt_labels_path}")
    
    pred_labels = load_nii(pred_labels_path)
    gt_labels = load_nii(gt_labels_path)
    
    # Apply mappings to convert to binary if provided
    if pred_mapping_path:
        pred_binary = apply_mapping(pred_labels, pred_mapping_path)
    else:
        pred_binary = (pred_labels > 0).astype(np.uint8)
    
    if gt_mapping_path:
        gt_binary = apply_mapping(gt_labels, gt_mapping_path)
    else:
        gt_binary = (gt_labels > 0).astype(np.uint8)
    
    # Calculate metrics
    metrics = {
        'case': case_name,
        'dice': calculate_dice(pred_binary, gt_binary),
        'iou': calculate_iou(pred_binary, gt_binary),
        'sensitivity': calculate_sensitivity(pred_binary, gt_binary),
        'specificity': calculate_specificity(pred_binary, gt_binary),
        'hausdorff': calculate_hausdorff_distance(pred_binary, gt_binary)
    }
    
    return metrics

def main():
    parser = argparse.ArgumentParser(description="Compare GMM labels with ground truth")
    parser.add_argument("--pred_dir", required=True, type=Path, 
                       help="Directory with predicted labels (GMM output)")
    parser.add_argument("--gt_dir", required=True, type=Path,
                       help="Directory with ground truth labels")
    parser.add_argument("--prefix", default=None,
                       help="Only process cases with this prefix")
    parser.add_argument("--pred_mapping", type=Path, default=None,
                       help="Mapping CSV for predictions (defaults to pred_dir/config/mapping_binary.csv)")
    parser.add_argument("--gt_mapping", type=Path, default=None,
                       help="Mapping CSV for ground truth")
    parser.add_argument("--output_csv", type=Path, default=None,
                       help="Output CSV file for results (defaults to gmm_evaluation_results.csv)")
    
    args = parser.parse_args()
    
    # Set default paths
    if args.pred_mapping is None:
        args.pred_mapping = args.pred_dir / "config" / "mapping_binary.csv"
    
    if args.output_csv is None:
        args.output_csv = Path("gmm_evaluation_results.csv")
    
    # Discover cases in both directories
    pred_cases = discover_cases(args.pred_dir, args.prefix)
    gt_cases = discover_cases(args.gt_dir, args.prefix)
    
    # Find common cases
    pred_case_names = {case.name for case in pred_cases}
    gt_case_names = {case.name for case in gt_cases}
    common_cases = sorted(pred_case_names & gt_case_names)
    
    if not common_cases:
        print("No common cases found between prediction and ground truth directories.")
        sys.exit(1)
    
    print(f"Found {len(common_cases)} common cases to evaluate.")
    
    # Evaluate all cases
    results = []
    for case_name in common_cases:
        try:
            metrics = evaluate_case(args.pred_dir, args.gt_dir, case_name,
                                  args.pred_mapping, args.gt_mapping)
            results.append(metrics)
            print(f"✓ {case_name}: Dice={metrics['dice']:.4f}, IoU={metrics['iou']:.4f}")
        except Exception as e:
            print(f"✗ {case_name}: Error - {e}")
    
    if not results:
        print("No cases were successfully evaluated.")
        sys.exit(1)
    
    # Convert to DataFrame and calculate summary statistics
    df = pd.DataFrame(results)
    
    # Calculate summary statistics
    summary_stats = df[['dice', 'iou', 'sensitivity', 'specificity']].describe()
    
    # Save results
    df.to_csv(args.output_csv, index=False)
    print(f"\nResults saved to: {args.output_csv}")
    
    # Print summary
    print("\n" + "="*60)
    print("EVALUATION SUMMARY")
    print("="*60)
    print(f"Cases evaluated: {len(results)}")
    print(f"Mean Dice: {df['dice'].mean():.4f} ± {df['dice'].std():.4f}")
    print(f"Mean IoU:  {df['iou'].mean():.4f} ± {df['iou'].std():.4f}")
    print(f"Mean Sensitivity: {df['sensitivity'].mean():.4f} ± {df['sensitivity'].std():.4f}")
    print(f"Mean Specificity: {df['specificity'].mean():.4f} ± {df['specificity'].std():.4f}")
    
    if not df['hausdorff'].isna().all():
        print(f"Mean Hausdorff: {df['hausdorff'].mean():.2f} ± {df['hausdorff'].std():.2f}")

if __name__ == "__main__":
    main()
