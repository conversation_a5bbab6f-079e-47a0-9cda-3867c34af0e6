#!/usr/bin/env python3
"""
Compare GMM-generated labels with ground truth labels.
Calculates Dice coefficient for brain segmentation evaluation.

Usage:
    python gmm_loss.py --pred_dir ./synthstrip_data_v1.5_2d_gmm2_exp5 --gt_dir ./synthstrip_data_v1.5_2d --prefix fsm_t1
"""

import argparse
import sys
from pathlib import Path
import numpy as np
import nibabel as nib
import pandas as pd
from typing import Dict, List, Optional

def load_nii(path: Path) -> np.ndarray:
    """Load NIfTI file and return data as numpy array."""
    return nib.load(str(path)).get_fdata().astype(np.float32)

def discover_cases(root: Path, prefix: Optional[str] = None) -> List[Path]:
    """Find all case directories that contain labels.nii.gz files."""
    cases = []
    for case_dir in sorted([x for x in root.iterdir() if x.is_dir()]):
        if prefix and prefix not in case_dir.name:
            continue
        labels_path = case_dir / "labels.nii.gz"
        if labels_path.exists():
            cases.append(case_dir)
    return cases

def apply_mapping(labels: np.ndarray, mapping_path: Path) -> np.ndarray:
    """Apply mapping CSV to convert multi-class labels to binary."""
    if not mapping_path.exists():
        print(f"Warning: Mapping file not found at {mapping_path}")
        return (labels > 0).astype(np.uint8)
    
    try:
        mapping_df = pd.read_csv(mapping_path)
        if 'label' not in mapping_df.columns:
            raise ValueError("CSV must have a 'label' column")
        
        # Choose mapping column flexibly
        value_col = 'mapping' if 'mapping' in mapping_df.columns else 'out'
        if value_col not in mapping_df.columns:
            raise ValueError("CSV must have a 'mapping' or 'out' column")
        
        # Create mapping dictionary
        mapping_dict = dict(zip(mapping_df['label'].astype(int), 
                               mapping_df[value_col].astype(int)))
        
        # Apply mapping
        binary_labels = np.zeros_like(labels, dtype=np.uint8)
        for label_id, binary_val in mapping_dict.items():
            binary_labels[labels == label_id] = binary_val
            
        return binary_labels
        
    except Exception as e:
        print(f"Error applying mapping: {e}")
        return (labels > 0).astype(np.uint8)

def calculate_dice_per_class(pred: np.ndarray, gt: np.ndarray, class_id: int) -> float:
    """Calculate Dice coefficient for a specific class."""
    pred_class = (pred == class_id)
    gt_class = (gt == class_id)

    intersection = np.sum(pred_class & gt_class)
    total = np.sum(pred_class) + np.sum(gt_class)

    if total == 0:
        return 1.0 if intersection == 0 else 0.0

    return 2.0 * intersection / total

def calculate_multiclass_dice(pred: np.ndarray, gt: np.ndarray) -> Dict[str, float]:
    """Calculate Dice coefficient for each class present in the data."""
    results = {}

    # Get unique classes from both prediction and ground truth
    pred_classes = np.unique(pred[pred > 0])  # Exclude background (0)
    gt_classes = np.unique(gt[gt > 0])       # Exclude background (0)

    # Calculate Dice for each GMM component
    for gmm_class in pred_classes:
        dice_score = calculate_dice_per_class(pred, gt, gmm_class)
        results[f'gmm_{gmm_class}'] = dice_score

    # Calculate Dice for each ground truth class
    for gt_class in gt_classes:
        dice_score = calculate_dice_per_class(pred, gt, gt_class)
        results[f'gt_{gt_class}'] = dice_score

    # Overall binary Dice (any class vs background)
    pred_binary = pred > 0
    gt_binary = gt > 0
    intersection = np.sum(pred_binary & gt_binary)
    total = np.sum(pred_binary) + np.sum(gt_binary)
    if total > 0:
        results['binary_dice'] = 2.0 * intersection / total
    else:
        results['binary_dice'] = 1.0

    return results

def evaluate_case(pred_dir: Path, gt_dir: Path, case_name: str) -> Dict[str, float]:
    """Evaluate a single case and return multi-class Dice coefficients."""

    # Load prediction labels
    pred_labels_path = pred_dir / case_name / "labels.nii.gz"
    if not pred_labels_path.exists():
        raise FileNotFoundError(f"Prediction labels not found: {pred_labels_path}")

    # Load ground truth labels
    gt_labels_path = gt_dir / case_name / "labels.nii.gz"
    if not gt_labels_path.exists():
        raise FileNotFoundError(f"Ground truth labels not found: {gt_labels_path}")

    pred_labels = load_nii(pred_labels_path).astype(int)
    gt_labels = load_nii(gt_labels_path).astype(int)

    # Calculate multi-class Dice coefficients
    dice_results = calculate_multiclass_dice(pred_labels, gt_labels)

    # Add case name
    dice_results['case'] = case_name

    return dice_results

def main():
    parser = argparse.ArgumentParser(description="Compare GMM labels with ground truth")
    parser.add_argument("--pred_dir", required=True, type=Path, 
                       help="Directory with predicted labels (GMM output)")
    parser.add_argument("--gt_dir", required=True, type=Path,
                       help="Directory with ground truth labels")
    parser.add_argument("--prefix", default=None,
                       help="Only process cases with this prefix")
    parser.add_argument("--pred_mapping", type=Path, default=None,
                       help="Mapping CSV for predictions (defaults to pred_dir/config/mapping_binary.csv)")
    parser.add_argument("--gt_mapping", type=Path, default=None,
                       help="Mapping CSV for ground truth")
    parser.add_argument("--output_csv", type=Path, default=None,
                       help="Output CSV file for results (defaults to gmm_evaluation_results.csv)")
    
    args = parser.parse_args()
    
    # Set default paths
    if args.pred_mapping is None:
        args.pred_mapping = args.pred_dir / "config" / "mapping_binary.csv"
    
    if args.output_csv is None:
        args.output_csv = Path("gmm_evaluation_results.csv")
    
    # Discover cases in both directories
    pred_cases = discover_cases(args.pred_dir, args.prefix)
    gt_cases = discover_cases(args.gt_dir, args.prefix)
    
    # Find common cases
    pred_case_names = {case.name for case in pred_cases}
    gt_case_names = {case.name for case in gt_cases}
    common_cases = sorted(pred_case_names & gt_case_names)
    
    if not common_cases:
        print("No common cases found between prediction and ground truth directories.")
        sys.exit(1)
    
    print(f"Found {len(common_cases)} common cases to evaluate.")
    
    # Evaluate all cases
    results = []
    for case_name in common_cases:
        try:
            metrics = evaluate_case(args.pred_dir, args.gt_dir, case_name)
            results.append(metrics)
            binary_dice = metrics.get('binary_dice', 0)
            print(f"✓ {case_name}: Binary Dice={binary_dice:.4f}")
        except Exception as e:
            print(f"✗ {case_name}: Error - {e}")

    if not results:
        print("No cases were successfully evaluated.")
        sys.exit(1)

    # Convert to DataFrame
    df = pd.DataFrame(results)

    # Save results
    df.to_csv(args.output_csv, index=False)
    print(f"\nResults saved to: {args.output_csv}")

    # Print summary
    print("\n" + "="*50)
    print("MULTI-CLASS DICE EVALUATION SUMMARY")
    print("="*50)
    print(f"Cases evaluated: {len(results)}")

    # Summary for binary dice
    if 'binary_dice' in df.columns:
        print(f"Binary Dice (overall): {df['binary_dice'].mean():.4f} ± {df['binary_dice'].std():.4f}")

    # Summary for GMM components
    gmm_cols = [col for col in df.columns if col.startswith('gmm_')]
    if gmm_cols:
        print(f"\nGMM Components found: {len(gmm_cols)}")
        for col in sorted(gmm_cols):
            mean_dice = df[col].mean()
            std_dice = df[col].std()
            print(f"  {col}: {mean_dice:.4f} ± {std_dice:.4f}")

    # Summary for ground truth classes
    gt_cols = [col for col in df.columns if col.startswith('gt_')]
    if gt_cols:
        print(f"\nGround Truth Classes found: {len(gt_cols)}")
        for col in sorted(gt_cols):
            mean_dice = df[col].mean()
            std_dice = df[col].std()
            print(f"  {col}: {mean_dice:.4f} ± {std_dice:.4f}")

if __name__ == "__main__":
    main()
