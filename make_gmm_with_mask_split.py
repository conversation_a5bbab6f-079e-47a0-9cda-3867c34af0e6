#!/usr/bin/env python3
# Two-phase GMM over an entire dataset, using the PER-SAMPLE mask in each folder.
# Layout of each sample dir (inside --src):
#   <case_dir>/
#     image.nii.gz
#     mask.nii.gz
#     labels.nii.gz   # (ignored; we regenerate labels in --dst)
#
# Output (in --dst) mirrors the same tree and writes:
#   <case_dir>/labels.nii.gz   # new, two-phase GMM labels (uint16)
#   <case_dir>/image.nii.gz    # copied if --copy_inputs
#   <case_dir>/mask.nii.gz     # copied if --copy_inputs
#   config/mapping_binary.csv  # label,mapping,class_name (brain=1..K_brain → 1; non-brain → 0)

import argparse, csv, sys, shutil
from pathlib import Path
import numpy as np
import nibabel as nib
from sklearn.mixture import GaussianMixture

# ---------- discovery ----------

def discover_sample_dirs(root: Path, image_name: str, mask_name: str, prefix: str | None):
    """
    Find all immediate subfolders that contain image & mask filenames.
    If prefix is given, only keep folders whose name contains it.
    """
    out = []
    for p in sorted([x for x in root.iterdir() if x.is_dir()]):
        if prefix and (prefix not in p.name):
            continue
        if (p / image_name).exists() and (p / mask_name).exists():
            out.append(p)
    return out

# ---------- io helpers ----------

def load_nii(p: Path):
    img = nib.load(str(p))
    return img.get_fdata(dtype=np.float32), img.affine, img.header

def save_like(arr: np.ndarray, affine, header, out_path: Path, dtype=np.uint16):
    out_path.parent.mkdir(parents=True, exist_ok=True)
    nib.save(nib.Nifti1Image(arr.astype(dtype), affine, header), str(out_path))

# ---------- math helpers ----------

def robust_norm(vals: np.ndarray):
    v = vals.astype(np.float32)
    if v.size == 0:
        return v
    p25, p50, p75 = np.percentile(v, [25, 50, 75])
    scale = (p75 - p25) + 1e-6
    return (v - p50) / scale

def fit_gmm_1d(x: np.ndarray, K: int, cov: str, reg: float, seed: int, subsample: int | None):
    """
    x: 1D vector (float), flattened region intensities (already normalized).
    Returns a trained GaussianMixture or None if region too small/constant.
    """
    x = x.reshape(-1, 1)
    n = x.shape[0]
    if n == 0:
        return None
    # if region is (almost) constant, GMM is pointless — skip
    if float(np.var(x)) < 1e-10:
        return None
    if subsample and subsample > 0 and n > subsample:
        rng = np.random.RandomState(seed)
        idx = rng.choice(n, size=subsample, replace=False)
        x_fit = x[idx]
    else:
        x_fit = x
    k_eff = min(K, x_fit.shape[0])  # scikit requires n_components <= n_samples
    if k_eff < 1:
        return None
    gm = GaussianMixture(
        n_components=k_eff, covariance_type=cov, reg_covar=reg,
        random_state=seed, init_params="kmeans", max_iter=300, tol=1e-4
    )
    gm.fit(x_fit)
    # predict on full (not subsampled) set
    gm._fitted_on_full = x  # keep for clarity/debug (unused)
    return gm

# ---------- core ----------

def two_phase_gmm_labels(image: np.ndarray,
                         mask_bool: np.ndarray,
                         K_brain: int, K_bg: int,
                         cov: str, reg: float, seed: int,
                         subsample: int | None):
    """
    ID scheme (GLOBAL & CONSISTENT across dataset):
      Brain components     : 1 .. K_brain
      Non-brain components : K_brain+1 .. K_brain+K_bg
    Works with 2D or 3D volumes.
    """
    shp = image.shape
    img_flat = image.reshape(-1).astype(np.float32)
    m_flat   = mask_bool.reshape(-1)

    # normalize brain and non-brain separately (robust)
    z = np.zeros_like(img_flat, dtype=np.float32)
    if m_flat.any():
        z[m_flat]  = robust_norm(img_flat[m_flat])
    if (~m_flat).any():
        z[~m_flat] = robust_norm(img_flat[~m_flat])

    labels_flat = np.zeros_like(img_flat, dtype=np.int32)

    # --- brain region ---
    if m_flat.any() and K_brain > 0:
        gm_b = fit_gmm_1d(z[m_flat], K_brain, cov, reg, seed, subsample)
        if gm_b is not None:
            labels_flat[m_flat] = gm_b.predict(z[m_flat].reshape(-1,1)) + 1  # 1..<=K_brain
        else:
            labels_flat[m_flat] = 1
    # else: stays 0 outside mask; we set non-brain below

    # --- non-brain region ---
    if (~m_flat).any() and K_bg > 0:
        gm_nb = fit_gmm_1d(z[~m_flat], K_bg, cov, reg, seed+1, subsample)
        if gm_nb is not None:
            labels_flat[~m_flat] = gm_nb.predict(z[~m_flat].reshape(-1,1)) + (K_brain + 1)
        else:
            labels_flat[~m_flat] = K_brain + 1

    return labels_flat.reshape(shp)

def write_gmm_mapping_csv(K_brain: int, K_bg: int, out_csv: Path):
    """
    Columns: label, mapping, class_name   (matches your mapping.csv schema)
    mapping: 1 = brain, 0 = non-brain
    """
    out_csv.parent.mkdir(parents=True, exist_ok=True)
    with open(out_csv, "w", newline="") as f:
        w = csv.writer(f)
        w.writerow(["label", "mapping", "class_name"])
        for lab in range(1, K_brain + 1):
            w.writerow([lab, 1, f"brain_gmm_{lab}"])
        for j in range(1, K_bg + 1):
            lab = K_brain + j
            w.writerow([lab, 0, f"nonbrain_gmm_{j}"])

def process_case(case_dir: Path, dst_root: Path, args):
    img_p = case_dir / args.image_filename
    msk_p = case_dir / args.mask_filename
    if not img_p.exists() or not msk_p.exists():
        print(f"[skip] missing image or mask in: {case_dir}", file=sys.stderr)
        return False

    image, aff, hdr = load_nii(img_p)
    mask,  _,  _    = load_nii(msk_p)

    # Accept 2D or 3D. If 3D, run GMM over all voxels in each region
    mask_bool = mask > 0.5

    # Handle NaNs/Infs gracefully
    image = np.nan_to_num(image, nan=np.nanmedian(image), posinf=np.max(image[np.isfinite(image)]), neginf=np.min(image[np.isfinite(image)]))

    labels = two_phase_gmm_labels(
        image=image, mask_bool=mask_bool,
        K_brain=args.K_brain, K_bg=args.K_bg,
        cov=args.cov, reg=args.reg, seed=args.seed,
        subsample=args.subsample
    )

    dst_case = dst_root / case_dir.name
    dst_case.mkdir(parents=True, exist_ok=True)

    # Always WRITE fresh labels.nii.gz (do NOT copy the old labels from src)
    save_like(labels, aff, hdr, dst_case / args.truth_filename, dtype=np.uint16)

    # Optionally copy inputs (so training can point to --dst only)
    if args.copy_inputs:
        shutil.copy2(img_p, dst_case / args.image_filename)
        shutil.copy2(msk_p, dst_case / args.mask_filename)

    return True

def main():
    ap = argparse.ArgumentParser(description="Two-phase GMM over dataset (per-sample masks; no global mask).")
    ap.add_argument("--src", required=True, type=Path, help="Dataset root containing sample folders")
    ap.add_argument("--dst", required=True, type=Path, help="Output root; mirrors sample folders")
    ap.add_argument("--image_filename", default="image.nii.gz")
    ap.add_argument("--mask_filename",  default="mask.nii.gz")
    ap.add_argument("--truth_filename", default="labels.nii.gz")  # training expects this name
    ap.add_argument("--prefix", default=None, help="Optional: only process folders whose name contains this string (e.g., 'fsm_t1')")
    ap.add_argument("--K_brain", type=int, default=3)
    ap.add_argument("--K_bg",    type=int, default=2)
    ap.add_argument("--cov", choices=["full","tied","diag","spherical"], default="full")
    ap.add_argument("--reg", type=float, default=1e-6)
    ap.add_argument("--seed", type=int, default=612385)
    ap.add_argument("--subsample", type=int, default=20000, help="Cap points per region for GMM fit (<=0 to disable)")
    ap.add_argument("--copy_inputs", action="store_true", help="Also copy image/mask into --dst case folders")
    ap.add_argument("--mapping_csv", type=Path, default=None, help="Defaults to <dst>/config/mapping_binary.csv")
    args = ap.parse_args()

    if args.subsample is not None and args.subsample <= 0:
        args.subsample = None

    # discover cases by presence of image & mask (optionally filter by prefix)
    cases = discover_sample_dirs(args.src, args.image_filename, args.mask_filename, args.prefix)
    if not cases:
        print(f"No sample folders found under {args.src} (prefix={args.prefix})", file=sys.stderr)
        sys.exit(2)

    args.dst.mkdir(parents=True, exist_ok=True)

    n_ok = 0
    for case in cases:
        n_ok += int(process_case(case, args.dst, args))

    if n_ok == 0:
        print("No cases processed.", file=sys.stderr)
        sys.exit(3)

    # global mapping (covers all possible component IDs)
    mapping_csv = args.mapping_csv or (args.dst / "config" / "mapping_binary.csv")
    write_gmm_mapping_csv(args.K_brain, args.K_bg, mapping_csv)

    print(f"Processed {n_ok}/{len(cases)} cases.")
    print(f"Mapping written to: {mapping_csv}")

if __name__ == "__main__":
    main()
